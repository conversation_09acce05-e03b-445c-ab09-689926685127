.file-upload {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 150px;
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  padding: 24px;
  box-sizing: border-box;
  text-align: center;
}

.file-upload:hover, .file-upload.active {
  border-color: var(--color-brand);
  background-color: var(--color-bg-hover);
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.file-upload .upload-icon {
  font-size: 48px;
  color: var(--color-content-regular);
}
.file-upload .upload-icon:hover {
  color: var(--color-brand);
} 
.file-upload .title {
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
  margin: 0;
}

.file-upload .subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
  margin: 0;
}

.file-upload .upload-button {
  margin-top: 8px;
}

.file-upload-preview {
  width: 100%;
  height: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: var(--radius-sm);
}

.file-upload-status {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.upload-message {
  font-size: var(--font-size-base);
  color: var(--color-content-accent);
  margin: 0;
}

.progress-bar-container {
  width: 80%;
  height: 8px;
  background-color: var(--color-support);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  transition: width 0.3s ease;
}

.upload-percentage {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}
