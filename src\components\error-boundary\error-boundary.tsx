import React, { Component, ReactNode } from 'react';
import { AlertTriangle } from 'lucide-react';
import './error-boundary.css';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="error-boundary">
          <div className="error-content">
            <AlertTriangle className="error-icon" size={48} />
            <h3 className="error-title">加载失败</h3>
            <p className="error-message">
              {this.state.error?.message || '模型加载时发生错误，请检查文件格式或网络连接'}
            </p>
            <button 
              className="error-retry-button"
              onClick={() => this.setState({ hasError: false, error: undefined })}
            >
              重试
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
