.input-box-container {
  display: flex;
  align-items: center;
  background-color: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: all 0.2s;
}

.input-box-container.focused {
  border-color: var(--color-brand);
}

.input-box-container.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 大小变体 */
.input-box-container.small {
  height: var(--input-height-small);
}

.input-box-container.medium {
  height: var(--input-height-medium);
}

.input-box-container.large {
  height: var(--input-height-large);
}

.input-box {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  padding: 0 var(--spacing-base);
  outline: none;
  width: 100%;
  height: 100%;
}

.input-box::placeholder {
  color: var(--color-content-secondary);
}

.input-box.has-prefix {
  padding-left: 0;
}

.input-box.has-suffix {
  padding-right: 0;
}

.input-prefix-icon,
.input-suffix-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-base);
  color: var(--color-content-regular);
}

.input-box-container:hover:not(.disabled) {
  border-color: var(--color-content-secondary);
}

.input-box-container.disabled .input-box {
  cursor: not-allowed;
}