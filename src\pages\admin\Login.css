.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-bg-page);
}

.login-card {
  width: 400px;
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  padding: 32px;
  box-shadow: var(--shadow-modal);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.login-logo {
  height: 24px;
  margin-bottom: 16px;
}

.login-header h2 {
  color: var(--color-content-accent);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}

.login-error {
  color: #ff4d4f;
  font-size: var(--font-size-sm);
  text-align: center;
}
