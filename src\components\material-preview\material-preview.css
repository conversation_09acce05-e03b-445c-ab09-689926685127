/* 材质预览组件样式 */
.material-preview {
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-bg-overlay);
  position: relative;
}

.material-preview canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

/* 加载状态 */
.material-preview--loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-overlay);
}

.material-preview--loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-content-mute);
  border-top-color: var(--color-content-accent);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  to {
    transform: rotate(360deg);
  }
}
