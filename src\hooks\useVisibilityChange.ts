import { useEffect, useState } from 'react';

/**
 * 自定义Hook，用于监听页面可见性变化和应用焦点状态
 * 返回一个布尔值，表示页面是否应该暂停渲染
 */
export const useVisibilityChange = () => {
  const [shouldPause, setShouldPause] = useState(false);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setShouldPause(document.hidden);
    };

    const handleFocusChange = () => {
      setShouldPause(!document.hasFocus());
    };

    // 初始状态
    setShouldPause(document.hidden || !document.hasFocus());

    // 添加事件监听
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocusChange);
    window.addEventListener('blur', handleFocusChange);

    // 清理事件监听
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocusChange);
      window.removeEventListener('blur', handleFocusChange);
    };
  }, []);

  return shouldPause;
};