.notification-container {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  display: flex;
  flex-direction: column-reverse;
  gap: var(--spacing-sm);
  z-index: 2000;
  align-items: flex-start;
}

.notification {
  padding: 10px var(--spacing-md);
  border-radius: var(--radius-base);
  color: var(--color-content-invert);
  box-shadow: var(--shadow-hover);
  animation: fadeInOut 3s ease-in-out;
  font-size: var(--font-size-base);
  white-space: nowrap;
}

.notification.success { background: var(--color-success); }
.notification.error { background: var(--color-error); }
.notification.info { background: var(--color-brand); }

@keyframes fadeInOut {
  0%, 100% { opacity: 0; transform: translateY(var(--spacing-lg)); }
  10%, 90% { opacity: 1; transform: translateY(0); }
}