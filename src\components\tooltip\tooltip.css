.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  width: var(--tooltip-width);
  background-color: var(--color-bg-dialog);
  color: var(--color-content-regular);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  box-sizing: border-box;
  z-index: 1000;
  pointer-events: none;
  animation: fadeIn 0.2s ease-in-out;
}

.tooltip::before {
  content: '';
  position: absolute;
  border: var(--spacing-s) solid transparent;
}

/* 顶部提示 */
.tooltip--top::before {
  border-top-color: var(--color-bg-dialog);
  bottom: calc(-1 * var(--spacing-base));
  left: 50%;
  transform: translateX(-50%);
}

/* 底部提示 */
.tooltip--bottom::before {
  border-bottom-color: var(--color-bg-dialog);
  top: calc(-1 * var(--spacing-base));
  left: 50%;
  transform: translateX(-50%);
}

/* 左侧提示 */
.tooltip--left::before {
  border-left-color: var(--color-bg-dialog);
  right: calc(-1 * var(--spacing-base));
  top: 50%;
  transform: translateY(-50%);
}

/* 右侧提示 */
.tooltip--right::before {
  border-right-color: var(--color-bg-dialog);
  left: calc(-1 * var(--spacing-base));
  top: 50%;
  transform: translateY(-50%);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
