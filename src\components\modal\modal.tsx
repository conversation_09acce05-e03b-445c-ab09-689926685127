import React from 'react';
import { X } from 'lucide-react';
import { IconButton } from '../icon-button/icon-button';
import './modal.css';

export interface ModalProps {
  /** 是否显示模态框 */
  visible: boolean;
  /** 模态框标题 */
  title: string;
  /** 模态框内容 */
  children: React.ReactNode;
  /** 关闭回调 */
  onClose: () => void;
  /** 模态框尺寸，默认为 'medium' */
  size?: 'small' | 'medium' | 'large' | 'full';
  /** 自定义类名 */
  className?: string;
  /** 是否显示关闭按钮，默认为 true */
  showCloseButton?: boolean;
  /** 点击遮罩层是否关闭，默认为 true */
  closeOnOverlayClick?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  title,
  children,
  onClose,
  size = 'medium',
  className = '',
  showCloseButton = true,
  closeOnOverlayClick = true,
}) => {
  if (!visible) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  };

  const modalClasses = [
    'modal-container',
    `modal-container--${size}`,
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className={modalClasses} onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">{title}</h2>
          {showCloseButton && (
            <IconButton 
              icon={X} 
              onClick={onClose} 
              size="small" 
              className="modal-close-button"
            />
          )}
        </div>
        <div className="modal-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
