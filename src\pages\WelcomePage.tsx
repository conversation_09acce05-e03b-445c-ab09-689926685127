import React, { useState, useEffect, useRef } from 'react';
import { ModelCard } from '../components/model-card/model-card';
import { Loading } from '../components/loading/loading';
import { UploadModelCard } from '../components/upload-model-card/upload-model-card';
import { apiService } from '../services/api';
import type { ModelData } from '../services/api';
import './WelcomePage.css';
import LogoSvg from '../assets/images/Logo.svg';

interface WelcomePageProps {
  theme?: 'light' | 'dark';
}

const WelcomePage: React.FC<WelcomePageProps> = ({ theme = 'dark' }) => {
  const [models, setModels] = useState<ModelData[]>([]);
  const [loading, setLoading] = useState(true);
  const [logoClickCount, setLogoClickCount] = useState(0);
  const logoClickTimer = useRef<number | null>(null);

  // 获取模型数据
  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true);
      try {
        const modelData = await apiService.getModels();
        setModels(modelData);
      } catch (error) {
        console.error('获取模型数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  // Logo点击处理（后台管理入口）
  const handleLogoClick = () => {
    const newCount = logoClickCount + 1;
    setLogoClickCount(newCount);
    
    // 清除之前的计时器
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
    }
    
    // 设置新的计时器，2秒内未达到3次点击则重置计数
    logoClickTimer.current = window.setTimeout(() => {
      setLogoClickCount(0);
    }, 2000);
    
    // 如果达到3次点击，跳转到后台登录页面
    if (newCount === 3) {
      setLogoClickCount(0);
      if (logoClickTimer.current) {
        window.clearTimeout(logoClickTimer.current);
      }
      window.location.href = '/admin';
    }
  };

  return (
    <div className={`welcome-page theme-${theme}`}>
      {/* Logo 直接放在页面左上角 */}
      <img
        className="welcome-logo"
        src={LogoSvg}
        alt="会通智能色彩云库"
        onClick={handleLogoClick}
      />

      {/* 主要内容区域 */}
      <main className="welcome-main">
        <div className="welcome-content">

          {/* 欢迎文字 */}
          <div className="welcome-text">
            <h1 className="welcome-greeting">您好！欢迎使用</h1>
            <h2 className="welcome-title">会通智能色彩云库</h2>
            <p className="welcome-subtitle">请选择一个模型进入渲染</p>
          </div>

          {/* 模型网格 */}
          <div className="welcome-models">
            {loading ? (
              <Loading
                text="正在加载模型..."
                size="large"
                variant="minimal"
              />
            ) : models.length > 0 ? (
              <div className="models-grid">
                {models.map((model) => (
                  <ModelCard key={model.id} model={model} />
                ))}
                <UploadModelCard />
              </div>
            ) : (
              <div className="models-grid">
                <UploadModelCard />
                <div className="welcome-empty">
                  <div className="empty-icon">📦</div>
                  <h3 className="empty-title">暂无可用模型</h3>
                  <p className="empty-description">
                    请联系管理员添加模型到云库中
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default WelcomePage;