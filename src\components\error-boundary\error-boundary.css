.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--spacing-xl);
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-icon {
  color: var(--color-danger);
  margin-bottom: var(--spacing-md);
}

.error-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0 0 var(--spacing-base) 0;
}

.error-message {
  font-size: var(--font-size-base);
  color: var(--color-content-secondary);
  margin: 0 0 var(--spacing-lg) 0;
  line-height: var(--line-height-base);
}

.error-retry-button {
  display: inline-flex;
  align-items: center;
  height: var(--button-height);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-brand);
  color: var(--color-content-invert);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background 0.2s ease;
}

.error-retry-button:hover {
  background: var(--color-brand-hover);
}
