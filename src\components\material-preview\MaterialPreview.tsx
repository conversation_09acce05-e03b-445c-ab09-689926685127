import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Stage } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialSettings } from '../custom-material-panel/custom-material-panel';
import './material-preview.css';

interface MaterialPreviewProps {
  settings: MaterialSettings;
  /**
   * Canvas 容器尺寸（宽高一致，默认 120px）。
   * 传入 0 或 undefined 时使用父元素 100% 宽高
   */
  size?: number;
}

/**
 * 后台材质编辑弹窗中的实时材质预览。
 * 使用与前台渲染页完全一致的环境贴图和灯光配置，确保所见即所得。
 */
const MaterialPreview: React.FC<MaterialPreviewProps> = ({ settings, size = 120 }) => {
  const sphereRef = useRef<THREE.Mesh>(null);

  // 使用useMemo缓存纹理，避免每次渲染都重新加载
  const texture = useMemo(() => {
    if (!settings.textureUrl) return null;
    return new THREE.TextureLoader().load(settings.textureUrl);
  }, [settings.textureUrl]);

  /**
   * 旋转动画，让材质球缓慢转动，展示高光和金属度效果。
   */
  const RotatingSphere: React.FC = () => {
    useFrame(() => {
      if (sphereRef.current) {
        sphereRef.current.rotation.y += 0.01;
      }
    });

    return (
      <mesh ref={sphereRef}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshStandardMaterial
          color={new THREE.Color(settings.color)}
          metalness={settings.metalness}
          roughness={settings.roughness}
          transparent={settings.opacity < 1}
          opacity={settings.opacity}
          map={texture}
        />
      </mesh>
    );
  };

  return (
    <div
      className="material-preview"
      style={{
        width: size || '100%',
        height: size || '100%',
      }}
    >
      <Canvas
        style={{ borderRadius: '50%' }}
        camera={{ position: [0, 0, 3], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* 与 RenderPage 完全一致的 Stage 配置 */}
        <Stage
          environment="city"
          intensity={0.6}
          adjustCamera={false}
          shadows={false}
          preset="rembrandt"
          scale={1}
        >
          <RotatingSphere />
        </Stage>
      </Canvas>
    </div>
  );
};

export default MaterialPreview;