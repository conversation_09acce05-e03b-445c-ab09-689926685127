/* 加载组件基础样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  white-space: nowrap;
}

.loading--centered {
  width: 100%;
  height: 100%;
  min-height: 60px;
}

/* 加载动画 */
.loading__spinner {
  border-radius: 50%;
  border-style: solid;
  border-color: var(--color-content-mute);
  border-top-color: var(--color-content-accent);
  animation: loading-spin 1s linear infinite;
  flex-shrink: 0;
}

.loading__text {
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  line-height: 1;
  margin: 0;
}

/* 尺寸变体 */
.loading--small .loading__spinner {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.loading--small .loading__text {
  font-size: var(--font-size-sm);
}

.loading--medium .loading__spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.loading--medium .loading__text {
  font-size: var(--font-size-base);
}

.loading--large .loading__spinner {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.loading--large .loading__text {
  font-size: var(--font-size-lg);
}

/* 样式变体 */
.loading--default {
  flex-direction: row;
}

.loading--overlay {
  flex-direction: row;
  background: var(--loading-container-bg);
  padding: 12px 20px;
  border-radius: 20px;
  backdrop-filter: var(--loading-container-backdrop-filter);
}

.loading--inline {
  flex-direction: row;
  display: inline-flex;
  min-height: auto;
}

.loading--minimal {
  flex-direction: column;
  gap: 12px;
}

.loading--minimal .loading__text {
  color: var(--color-content-secondary);
}

/* 旋转动画 */
@keyframes loading-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* 特殊场景：页面级加载 */
.loading--page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--loading-backdrop);
  z-index: 9999;
  flex-direction: column;
  gap: 16px;
}

.loading--page .loading__spinner {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

.loading--page .loading__text {
  font-size: var(--font-size-xl);
}
