import React, { useState } from 'react';
import type { InputHTMLAttributes } from 'react';
import { Search } from 'lucide-react';
import './search-box.css';

interface SearchBoxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  /** 占位文本 */
  placeholder?: string;
  /** 默认值 */
  defaultValue?: string;
  /** 搜索框宽度 */
  width?: string | number;
  /** 值变化回调 */
  onChange?: (value: string) => void;
  /** 搜索按钮点击回调 */
  onSearch?: (value: string) => void;
  /** 自定义类名 */
  className?: string;
  /** 禁用状态 */
  disabled?: boolean;
}

export const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = '搜索',
  defaultValue = '',
  width = '100%',
  onChange,
  onSearch,
  className = '',
  disabled = false,
  ...restProps
}) => {
  const [value, setValue] = useState<string>(defaultValue);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    if (onChange) onChange(newValue);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch(value);
    }
  };
  
  const handleSearchClick = () => {
    if (onSearch) onSearch(value);
  };
  
  const searchBoxStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
  };
  
  return (
    <div 
      className={`search-box ${disabled ? 'search-box--disabled' : ''} ${className}`}
      style={searchBoxStyle}
      data-layer="search-box"
    >
      <div className="search-box__icon-container" onClick={handleSearchClick}>
        <Search size={16} className="search-box__icon" />
      </div>
      <input
        type="text"
        className="search-box__input"
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        {...restProps}
      />
    </div>
  );
};
